<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>AI Interview Scorer</title>
  <style>
    body { font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: auto; }
    .question-box { margin-bottom: 20px; }
    textarea { width: 100%; height: 100px; margin-top: 10px; }
    button { padding: 10px 20px; font-size: 16px; }
    .score { margin-top: 10px; color: green; }
    .feedback { margin-top: 5px; font-style: italic; }
  </style>
</head>
<body>
  <h2>AI Interview Bot</h2>
  <div id="question-box" class="question-box"></div>
  <textarea id="answer" placeholder="Type your answer..."></textarea><br><br>
  <button onclick="submitAnswer()">Submit Answer</button>

  <div id="results"></div>

  <script>
    // WARNING: API key should NOT be exposed in client-side code in production!
    // This is for demonstration purposes only. Use a backend server to proxy API calls.
    const apiKey = "********************************************************************************************************************************************************************";
    const questions = [
      "Why do you want to work at our company?",
      "Tell me about a challenge you faced and how you overcame it.",
      "What are your greatest strengths and weaknesses?",
      "How do you handle tight deadlines or pressure at work?",
      "Where do you see yourself in the next 5 years?"
    ];

    let currentQuestion = 0;
    let scores = [];

    document.getElementById("question-box").innerText = questions[currentQuestion];

    async function submitAnswer() {
      const answer = document.getElementById("answer").value;
      if (!answer.trim()) return alert("Please enter your answer.");

      // Call the backend API for real AI evaluation
      try {
        let content;

        const res = await fetch("http://localhost:5000/api/evaluate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            question: questions[currentQuestion],
            answer: answer
          }),
        });

        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status} - ${res.statusText}`);
        }

        const data = await res.json();
        content = data.evaluation;

        try {
          const parsed = JSON.parse(content);
          scores.push(parsed.score);

          const resultDiv = document.createElement("div");
          resultDiv.innerHTML = `
            <div class="score">Score: ${parsed.score}/10</div>
            <div class="feedback">Feedback: ${parsed.feedback}</div><hr>
          `;
          document.getElementById("results").appendChild(resultDiv);
        } catch (e) {
          alert("Failed to parse AI response. Please check OpenAI API output.");
          console.log(content);
          return;
        }

        currentQuestion++;
        document.getElementById("answer").value = "";

        if (currentQuestion < questions.length) {
          document.getElementById("question-box").innerText = questions[currentQuestion];
        } else {
          const total = scores.reduce((a, b) => a + b, 0);
          const average = (total / scores.length).toFixed(2);
          document.getElementById("question-box").innerText = `Interview Complete!`;
          document.getElementById("results").innerHTML += `<h3>Final Score: ${average}/10</h3>`;
        }

      } catch (error) {
        console.error("Error:", error);
        alert(`Error: ${error.message}\n\nMake sure:\n1. Backend server is running on http://localhost:5000\n2. OpenAI API key is valid\n3. Network connection is working\n\nRun 'npm run dev' in the backend directory to start the server.`);
      }
    }
  </script>
</body>
</html>
