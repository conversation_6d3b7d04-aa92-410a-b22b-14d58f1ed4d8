const axios = require('axios');

// Test the Gemini API integration
async function testGeminiEvaluation() {
  const testData = {
    question: "What is your greatest strength?",
    answer: "My greatest strength is my ability to work well in teams and communicate effectively with colleagues."
  };

  try {
    console.log('Testing Gemini evaluation endpoint...');
    console.log('Question:', testData.question);
    console.log('Answer:', testData.answer);
    console.log('---');

    const response = await axios.post('http://localhost:5000/api/evaluate', testData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Response received:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

    if (response.data.parsed) {
      console.log('---');
      console.log('Parsed evaluation:');
      console.log('Score:', response.data.parsed.score);
      console.log('Feedback:', response.data.parsed.feedback);
    }

  } catch (error) {
    console.error('Test failed:');
    console.error('Error:', error.response?.data || error.message);
  }
}

// Run the test
testGeminiEvaluation();
