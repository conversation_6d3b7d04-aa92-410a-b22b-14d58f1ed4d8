const express = require('express');
const cors = require('cors');
const fs = require('fs');
const speech = require('@google-cloud/speech');
const OpenAI = require('openai');
const app = express();
const port = 5000;

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************'
});

app.use(cors());
app.use(express.json({ limit: '10mb' }));

// Path to your JSON key (using the existing googleCloud.json file)
const client = new speech.SpeechClient({
  keyFilename: '../googleCloud.json',
});

app.post('/speech-to-text', async (req, res) => {
  try {
    const audio = req.body.audioBase64;
    
    if (!audio) {
      return res.status(400).json({ error: 'No audio data provided' });
    }

    const audioBuffer = Buffer.from(audio, 'base64');
    const audioBytes = audioBuffer.toString('base64');

    const audioConfig = {
      content: audioBytes,
    };

    const config = {
      encoding: 'WEBM_OPUS', // Changed from LINEAR16 to support web audio
      sampleRateHertz: 48000, // Changed to match typical web audio
      languageCode: 'en-US',
      enableAutomaticPunctuation: true,
      model: 'latest_long', // Use latest model for better accuracy
    };

    const request = {
      audio: audioConfig,
      config: config,
    };

    const [response] = await client.recognize(request);
    const transcription = response.results
      .map(result => result.alternatives[0].transcript)
      .join('\n');
    
    console.log('Transcription:', transcription);
    res.json({ transcription });
  } catch (err) {
    console.error('Speech recognition error:', err);
    res.status(500).json({ 
      error: 'Speech recognition failed',
      details: err.message 
    });
  }
});

// Interview evaluation endpoint
app.post('/api/evaluate', async (req, res) => {
  try {
    const { question, answer } = req.body;

    if (!question || !answer) {
      return res.status(400).json({ error: 'Question and answer are required' });
    }

    const prompt = `You're an AI interview evaluator. Evaluate the candidate's response to the following question:

Question: ${question}
Answer: ${answer}

Provide a score out of 10 and give brief constructive feedback.
Respond in JSON format only:
{
  "score": x,
  "feedback": "..."
}`;

    // Try different models in order of preference
    let completion;
    const modelsToTry = ["gpt-3.5-turbo", "gpt-4o-mini", "gpt-4", "gpt-3.5-turbo-0125"];

    for (const model of modelsToTry) {
      try {
        completion = await openai.chat.completions.create({
          model: model,
          messages: [{ role: "user", content: prompt }],
          temperature: 0.7,
          max_tokens: 200
        });
        console.log(`Successfully used model: ${model}`);
        break;
      } catch (modelError) {
        console.log(`Model ${model} failed:`, modelError.message);
        if (model === modelsToTry[modelsToTry.length - 1]) {
          throw modelError; // If all models fail, throw the last error
        }
      }
    }

    const content = completion.choices[0].message.content;

    try {
      const evaluation = JSON.parse(content);
      res.json({ evaluation: content, parsed: evaluation });
    } catch (parseError) {
      console.error('Failed to parse OpenAI response:', content);
      res.status(500).json({
        error: 'Failed to parse AI response',
        rawResponse: content
      });
    }

  } catch (error) {
    console.error('OpenAI API error:', error);
    res.status(500).json({
      error: 'Failed to evaluate answer',
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Speech-to-text and interview evaluation server is running' });
});

app.listen(port, () => {
  console.log(`Speech-to-text server running on http://localhost:${port}`);
});
